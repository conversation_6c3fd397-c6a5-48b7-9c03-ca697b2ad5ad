export type Message = {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  timestamp: number;
  roomId?: string;
};

export type ServerToClientEvents = {
  serverTime: (data: { time: number }) => void;
  messageReceived: (message: Message) => void;
  userJoined: (data: { userId: string; userName: string; roomId?: string }) => void;
  userLeft: (data: { userId: string; userName: string; roomId?: string }) => void;
  error: (data: { message: string }) => void;
};

export type ClientToServerEvents = {
  sendMessage: (data: { content: string; roomId?: string }) => void;
  joinRoom: (data: { roomId: string }) => void;
  leaveRoom: (data: { roomId: string }) => void;
  setUserInfo: (data: { userId: string; userName: string }) => void;
};

export type InterServerEvents = {
  ping: () => void;
};

export type SocketData = {
  userId?: string;
  userName?: string;
  rooms?: Set<string>;
};
