"use client";

import { io, Socket } from "socket.io-client";
import type { ServerToClientEvents, ClientToServerEvents } from "@/types/sockets";

class SocketManager {
  private socket: Socket<ServerToClientEvents, ClientToServerEvents> | null = null;
  private url: string;
  private isConnecting: boolean = false;

  constructor() {
    this.url = process.env.NEXT_PUBLIC_MESSAGE_SOCKET_URL as string;
    if (!this.url) console.error("NEXT_PUBLIC_MESSAGE_SOCKET_URL is not defined");
  }

  connect(): Socket<ServerToClientEvents, ClientToServerEvents> {
    if (this.socket?.connected) return this.socket;
    if (this.isConnecting) return this.socket!;

    this.isConnecting = true;

    if (!this.socket) {
      this.socket = io(this.url, {
        autoConnect: false,
        transports: ["websocket", "polling"],
        timeout: 5000,
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
      });

      this.socket.on("connect", () => {
        console.log("Connected to socket server:", this.socket?.id);
        this.isConnecting = false;
      });

      this.socket.on("disconnect", (reason) => {
        console.log("Disconnected from socket server:", reason);
        this.isConnecting = false;
      });

      this.socket.on("connect_error", (error) => {
        console.error("Socket connection error:", error);
        this.isConnecting = false;
      });
    }

    this.socket.connect();
    return this.socket;
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket.removeAllListeners();
      this.socket = null;
    }
    this.isConnecting = false;
  }

  getSocket(): Socket<ServerToClientEvents, ClientToServerEvents> | null {
    return this.socket;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }
}

const socketManager = new SocketManager();

export const socket = socketManager.connect();
export { socketManager };
