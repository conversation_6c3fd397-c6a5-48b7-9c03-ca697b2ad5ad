"use server";

import { createServer } from "http";
import { Server } from "socket.io";
import { randomUUID } from "node:crypto";
import type { ServerToClientEvents, ClientToServerEvents, InterServerEvents, SocketData, Message } from "@/types/sockets";

const httpServer = createServer();
const io = new Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>(httpServer, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
  },
});

io.on("connection", (socket) => {
  console.log("User connected:", socket.id);

  // Initialize socket data
  socket.data.rooms = new Set();

  // send server time immediately
  socket.emit("serverTime", { time: Date.now() });

  const interval = setInterval(() => {
    socket.emit("serverTime", { time: Date.now() });
  }, 1000);

  // Handle user info setting
  socket.on("setUserInfo", (data) => {
    socket.data.userId = data.userId;
    socket.data.userName = data.userName;
    console.log(`User ${data.userName} (${data.userId}) set info`);
  });

  // Handle message sending
  socket.on("sendMessage", (data) => {
    if (!socket.data.userId || !socket.data.userName) {
      socket.emit("error", { message: "User info not set" });
      return;
    }

    const message: Message = {
      id: randomUUID(),
      content: data.content,
      senderId: socket.data.userId,
      senderName: socket.data.userName,
      timestamp: Date.now(),
      roomId: data.roomId,
    };

    if (data.roomId) {
      // Send to specific room
      socket.to(data.roomId).emit("messageReceived", message);
    } else {
      // Broadcast to all connected clients
      socket.broadcast.emit("messageReceived", message);
    }

    console.log(`Message from ${socket.data.userName}: ${data.content}`);
  });

  // Handle room joining
  socket.on("joinRoom", (data) => {
    socket.join(data.roomId);
    socket.data.rooms?.add(data.roomId);

    if (socket.data.userId && socket.data.userName) {
      socket.to(data.roomId).emit("userJoined", {
        userId: socket.data.userId,
        userName: socket.data.userName,
        roomId: data.roomId,
      });
    }

    console.log(`User ${socket.data.userName} joined room ${data.roomId}`);
  });

  // Handle room leaving
  socket.on("leaveRoom", (data) => {
    socket.leave(data.roomId);
    socket.data.rooms?.delete(data.roomId);

    if (socket.data.userId && socket.data.userName) {
      socket.to(data.roomId).emit("userLeft", {
        userId: socket.data.userId,
        userName: socket.data.userName,
        roomId: data.roomId,
      });
    }

    console.log(`User ${socket.data.userName} left room ${data.roomId}`);
  });

  socket.on("disconnect", () => {
    clearInterval(interval);

    // Notify all rooms the user was in
    if (socket.data.rooms && socket.data.userId && socket.data.userName) {
      socket.data.rooms.forEach((roomId) => {
        socket.to(roomId).emit("userLeft", {
          userId: socket.data.userId!,
          userName: socket.data.userName!,
          roomId,
        });
      });
    }

    console.log("User disconnected:", socket.id);
  });
});

const PORT = process.env.SOCKET_PORT || "3001";
httpServer.listen(parseInt(PORT), () => {
  console.log(`Socket server running on port ${PORT}`);
});
